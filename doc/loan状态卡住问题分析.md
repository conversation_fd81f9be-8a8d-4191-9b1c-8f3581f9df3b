# Loan表状态卡在INIT问题分析报告

## 📋 问题描述

Flow系统中loan表记录一直卡在INIT状态，并且loan_record表没有对应的数据记录。

## 🔍 问题分析

### 1. 正常流程分析

根据代码分析，正常的loan状态流转应该是：

```
INIT → PROCESSING → SUCCESS/FAILED
```

**关键流程节点：**

1. **LoanService.apply()** - 创建loan记录，状态为INIT
2. **mqService.submitLoanApply()** - 发送MQ消息
3. **LoanApplyListener.loanApplyListen()** - 监听MQ消息
4. **LoanService.loanApply()** - 处理放款申请
5. **LoanService.genLoanRecord()** - 生成loan_record记录
6. **mqService.submitLoanRecordApply()** - 发送loan_record处理消息
7. **LoanRecordApplyListener** - 处理具体放款逻辑

### 2. 可能的卡住原因

#### 2.1 MQ消息处理异常

**问题点1：MQ消息未被消费**
- MQ服务可能未正常运行
- 队列配置错误或队列不存在
- 消息路由配置问题

**问题点2：消息处理异常**
```java
// LoanApplyListener.java 第41行
getMqWarningService().warn("放款申请异常:loanId," + loanId + "," + e.getMessage(), msg -> logger.error(msg, e));
```
- 异常被捕获但未重试
- 超过最大重试次数后停止处理

#### 2.2 业务逻辑校验失败

**挂起检查失败：**
```java
// LoanService.java 第160-175行
LoanSuspendFlag loanSuspendFlag = loanCommonService.suspendCheck(loan);
switch (loanSuspendFlag) {
    case IGNORE -> { return loan; }
    case SUSPEND -> {
        loan.setLoanState(ProcessState.SUSPEND);
        return loanRepository.save(loan);
    }
}
```

**可能的挂起原因：**
- 时间限制校验失败 (`timeSuspend()`)
- 额度限制校验失败 (`limitSuspend()`)
- 授信有效期校验失败 (`creditSuspend()`)

#### 2.3 数据库相关问题

**事务问题：**
- 数据库连接池耗尽
- 长事务导致锁等待
- 数据库死锁

**数据一致性问题：**
- loan记录存在但相关依赖数据缺失
- 外键约束问题

#### 2.4 前置条件检查失败

**流量三要素验证：**
```java
// LoanService.java 第154-158行
boolean isPass = preLoanService.flowPreLoanCheck(order);
if (!isPass) {
    loanCommonService.loanFail(loan);
    return loan;
}
```

**放款记录检查：**
```java
// LoanService.java 第189-192行
if (loanRecordRepository.existsByLoanIdAndLoanStateNotIn(loanId, ProcessState.FAILED)) {
    warningService.warn("放款申请异常,借据[" + loanId + "]存在非失败的放款记录", logger::error);
    return;
}
```

## 🔧 排查步骤

### 1. 检查MQ状态

```bash
# 检查RabbitMQ服务状态
systemctl status rabbitmq-server

# 检查队列状态
rabbitmqctl list_queues name messages consumers
```

**关键队列：**
- `loan.apply` - 放款申请队列
- `loan.record.apply` - 放款记录申请队列

### 2. 检查应用日志

**日志路径：**
- `/home/<USER>/logs/cash-business/cash-business.log`

**关键日志搜索：**
```bash
# 搜索放款申请相关日志
grep "监听放款申请" /home/<USER>/logs/cash-business/cash-business.log

# 搜索异常信息
grep "放款申请异常" /home/<USER>/logs/cash-business/cash-business.log

# 搜索挂起相关日志
grep "挂起校验" /home/<USER>/logs/cash-business/cash-business.log
```

### 3. 数据库检查

```sql
-- 检查卡住的loan记录
SELECT id, loan_state, apply_time, bank_channel, fail_reason, created_time
FROM loan 
WHERE loan_state = 'INIT' 
ORDER BY created_time DESC 
LIMIT 10;

-- 检查对应的order状态
SELECT o.id, o.order_state, l.id as loan_id, l.loan_state
FROM `order` o 
JOIN loan l ON o.id = l.order_id 
WHERE l.loan_state = 'INIT';

-- 检查是否有对应的loan_record
SELECT lr.* 
FROM loan_record lr 
JOIN loan l ON lr.loan_id = l.id 
WHERE l.loan_state = 'INIT';

-- 检查资方配置
SELECT bank_channel, enabled, loan_day_limit, updated_time
FROM capital_config;
```

### 4. 配置检查

**Apollo配置检查：**
- 数据库连接配置
- MQ连接配置
- 业务开关配置

## 🚨 常见解决方案

### 1. MQ问题解决

```bash
# 重启MQ服务
systemctl restart rabbitmq-server

# 清理死信队列
rabbitmqctl purge_queue loan.apply
```

### 2. 手动触发处理

```java
// 通过管理接口手动触发
@PostMapping("/manual/loan/apply/{loanId}")
public void manualLoanApply(@PathVariable String loanId) {
    loanService.loanApply(loanId);
}
```

### 3. 数据修复

```sql
-- 重置loan状态（谨慎操作）
UPDATE loan 
SET loan_state = 'INIT', apply_time = NOW() 
WHERE id = 'xxx' AND loan_state = 'INIT';
```

## 📊 监控建议

### 1. 添加监控指标

- loan表INIT状态记录数量
- MQ队列消息积压数量
- 放款申请异常次数

### 2. 告警规则

- INIT状态记录超过阈值时间告警
- MQ消息积压告警
- 放款申请异常频率告警

## 🔄 预防措施

1. **完善异常处理**：确保所有异常都有适当的重试机制
2. **增加监控**：实时监控关键业务指标
3. **定期巡检**：定期检查卡住的记录并处理
4. **优化配置**：调整MQ和数据库连接池配置
5. **业务开关**：增加业务降级开关，避免系统性问题

---

**建议优先检查：**
1. MQ服务状态和队列消息积压情况
2. 应用日志中的异常信息
3. 数据库中卡住记录的具体信息
4. 资方配置是否正常
